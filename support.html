
<!DOCTYPE html>
<html class="no-js" lang="zxx">
<head>
<meta charset="utf-8" />
<meta http-equiv="x-ua-compatible" content="ie=edge" />
<meta name="google-site-verification" content="c3nHakgUXNlzKDAvc80qcCUWCCU09RUYjXZ-FZZCgRo" />
<title>Get In Touch With Us Today for All Your Investment Needs</title>
<meta name="description" content="If you have any questions or want to know more about our investment solutions, feel free to contact us. Our team will get back to you.">
<meta name="keywords" content="Wise-Income Limited, Hyip, Paying Hyips, Hyip Monitor, Secure Investment, Online Investment, Trusted Online Investment, Top 1 Hyip, Top 10 Hyips, Long Term Online Investment, 100% Secure Online Business, Top Online Investment Company, Invest and Earn on Daily Bases, Online Investment Services, Investment, Invest with us, Invest with 100% Guaranty, Money Back Guaranty, Hot Investment Company, Risk Free Online Investment, Forex Trading, Forex, Stock Exchange, Real Estate, Invest in Forex Trading, Invest in Stock Exchange Markets, Buy Shares Online, Invest in Multinational Companies, Minimum Invest $10, Start Online Investment Just in $10, Daily earn Daily Withdraw, Minimum Withdraw $0.10, Good Hyip Sites , Top 10 Hyip sites Most Popular Crypto Currencies, Invest with crypto, Bitcoin investment, Tether investment, USDT investment, Ethereum Investment, DogeCoin investment, Tron Investment, LiteCoin investment, Invest with paypal, We accept paypal, invest with visa master card, we accept perfectmoney, Instant Withdraw, Passive income, Online Since 2010. Paying hyip sites, casino investment, gambling investment, Long term project, popular hyip sites, best online investment services, Investment management ">
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

	<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.png" />
	<!-- Place favicon.ico in the root directory -->

	<!-- Web Font -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;800&display=swap" rel="stylesheet">

<!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-+0n0xVW2eSR5OomGNYDnhzAbDsOXxcvSN1TPprVMTNDbiYZCxYbOOl7+AMvyTG2x" crossorigin="anonymous">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.3/font/bootstrap-icons.css">
	<link rel="stylesheet" href="assets/css/LineIcons.2.0.css" />
	<link rel="stylesheet" href="assets/css/animate.css" />
	<link rel="stylesheet" href="assets/css/custom.css?v5" />
	<link rel="stylesheet" href="assets/fonts/stylesheet.css" />
	
	<!-- ========================= JS here ========================= --> 
	<script src="http://ajax.googleapis.com/ajax/libs/jquery/1/jquery.min.js"></script>
	 <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-gtEjrD/SeCtmISkJkNUaaKMoLD0//ElJ19smozuHV6z3Iehds+3Ulb9Bn9Plx0x4" crossorigin="anonymous"></script>
	  <script src="assets/js/main.js"></script>
	 <script src="assets/js/wow.min.js"></script>
	 
	 <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-5Z21CYLGWV"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
    
      gtag('config', 'G-5Z21CYLGWV');
    </script>
	 
	 
	 
</head>

<body>
	
	
	
	

	
	

<section class="top-part pb-0">
<div class="top-part-inner">
<a class="telegram-top" href="#"target="_blank"></a>

		
<!-- ========================= header start ========================= -->	
<header class="header navbar-area">
  <div class="container">

  
  <div class="row">
	  
	  
	  <div class="col-lg-3">
	  <a class="navbar-brand" href="index.html"><img src="assets/img/logo.png"></a>
	  </div>
	  
	    <div class="col-lg-9">
			<div class="menu-area">
			
			<section class="prices">
					<div class="row">
						<div class="col-lg-12 px-0">
							<div class="price">
								<figure>
									<img src="assets/img/btc.png"> BTC
								</figure>
								<div class="value">
									<p class="bitCoin"></p>
								</div>
							</div>
							
							<div class="price">
								<figure>
									<img src="assets/img/eth.png"> ETH
									
								</figure>
								<div class="value">
									<p class="ethCoin"></p>
									
								</div>
							</div>
							
							<div class="price">
								<figure>
									<img src="assets/img/ltc.png"> LTC
								
								</figure>
								<div class="value">
									<p class="liteCoin"></p>
								
								</div>
							</div>
							
							<div class="price">
								<figure>
									<img src="assets/img/doge.png"> DOGE
									<i class="dogeCoin_change1"></i>
								</figure>
								<div class="value">
									<p class="dogeCoin"></p>
							
								</div>
							</div>
							
							
							
								<div class="price">
								<figure>
									<img src="assets/img/xrp.png"> XRP
								
								</figure>
								<div class="value">
									<p class="xrpCoin"></p>
									
								</div>
							</div>
							
								
							
							
						</div>
					</div>

				</section>
			
	  
		   <nav class="navbar navbar-expand-lg">
			
			<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
			  <i class="lni lni-menu"></i>
			</button>
			<div class="collapse navbar-collapse menu-left" id="navbarSupportedContent">
			
			  <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
				<li class="nav-item">
			  <a class="nav-link" href="home.html">Home</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="about.html">About us</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="rules.html">Rules</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="faq.html">Help Center</a>
				</li>
				
					<li class="nav-item">
				<a class="nav-link" href="support.html">Support</a>
				</li>
				
								<li class="nav-item">
				  <a class="nav-link" href="#">Register</a>
				</li>
				<li class="nav-item">
				  <a class="nav-link" href="#">Login</a>
				</li>
				
								
				
			  </ul>
			 
			</div>
		  </div>
			  
		</nav>
		
	
		
		</div>
		</div>
	</div><!--end row-->	
  </div>
  <!-- container --> 

</header>
<!-- ========================= header end ========================= -->	

<div class="banner-bottom banner-bottom-sub mt-2 mb-0">
	<div class="container">
		<div class="row">
			<div class="col-lg-12">
				<div class="banner-middle">
					<div class="page-title mt-3">
						<span>Request Assistance</span>
						<b>Support</b> Center
						</div>
				</div>
			</div>
		</div>
	</div>
</div>

</section>	


<section class="subpage_wrap">
	<div class="container">
		<div class="row">
			<div class="col-md-12">
						


<p class="text-center mb-4">Our experienced customer service team works 24 hours a day for 7 days a week to help with any questions.</p>
	
			
			
		

							
				<div class="login-form register-form">
					
				

				
				<script language=javascript>
								
				function checkform() {
				  if (document.mainform.name.value == '') {
					alert("Please type your full name!");
					document.mainform.name.focus();
					return false;
				  }
				  if (document.mainform.email.value == '') {
					alert("Please enter your e-mail address!");
					document.mainform.email.focus();
					return false;
				  }
				  if (document.mainform.message.value == '') {
					alert("Please type your message!");
					document.mainform.message.focus();
					return false;
				  }
				  return true;
				}
				
								</script>

				<form method=post name=mainform onsubmit="return checkform()"><input type="hidden" name="form_id" value="16880398924924"><input type="hidden" name="form_token" value="a388f7272e321a03b7cc13bba58efec6">
				<input type=hidden name=a value=support>
				<input type=hidden name=action value=send>

				 				 
				 
				<div class="mb-3">
				  <label class="form-label">Your Name</label>
								<input type="text" name="name" value="" size=30 class="form-control">
								</div>
				<div class="mb-3">
				 <label class="form-label">Your Email</label>
								<input type="text" name="email" value="" size=30 class="form-control">
								</div>
				<div class="mb-3">
					 <label class="form-label">Your Message</label>
					<textarea name=message class="form-control" cols=45 rows=3></textarea>
				</div>
				

<script src='https://www.google.com/recaptcha/api.js'></script>
<tr>
 <td class=menutxt colspan=2>
<div class="g-recaptcha" data-sitekey="6LdQwVMlAAAAAN5_O4kJSReQ0uWhHAPoTfE1q1ZP"></div>
 </td>
</tr>



				<div class="mb-3 mt-3"><input type=submit value="Send" class="btn btn-warning">
				</div>
				</form>

							
				
				
	

				</div>
			
			
				</div>


		</div>
		
		



		
	</div>
</section>	
<section class="affiliate_area">
	<div class="container">

		

		<div class="row">
			<div class="col-lg-12">
				<div class="payment-processors">
				
				
					<div class="payment-block">
						<figure><img src="assets/img/payment1.png"/></figure>
						<figure><img src="assets/img/payment2.png"/></figure>
						<figure><img src="assets/img/payment3.png"/></figure>
						<figure><img src="assets/img/payment4.png"/></figure>
						<figure><img src="assets/img/payment5.png"/></figure>
						<figure><img src="assets/img/payment6.png"/></figure>
						<figure><img src="assets/img/payment7.png"/></figure>
						<figure><img src="assets/img/payment8.png"/></figure>
						<figure><img src="assets/img/payment9.png"/></figure>
				</div>
				
				<div class="payment-block">		
						
						<figure><img src="assets/img/payment10.png"/></figure>
						<figure><img src="assets/img/payment11.png"/></figure>
						<figure><img src="assets/img/payment12.png"/></figure>
						<figure><img src="assets/img/payment13.png"/></figure>
						<figure><img src="assets/img/payment14.png"/></figure>
						<figure><img src="assets/img/payment15.png"/></figure>
						<figure><img src="assets/img/payment16.png"/></figure>
						
					</div>
					
						<hr>
					
				</div>
			</div>
		</div>
		
		
		
	


    <div class="row mt-3">
		
		
		<div class="col-md-12">
			
			<div class="row">
		
				<div class="col-md-4 col-sm-12">
					<div class="footer-copy">
						<img class="mb-2" src="assets/img/logo.png"/>
						<p>© 2023. Wise-Income Limited. All Rights Reserved.</p>
					</div>
				</div>
				
				<div class="col-md-8 col-sm-12 footer">
				
					<div class="row">
				
							<div class="col-md-4 col-sm-12">
							<h4>Company</h4>
							<ul>
								<li><a href="about.html">About us</a></li>
								<li><a href="faq.html">Help Center</a></li>
								<li><a href="rules.html">Terms of Services</a></li>
									<li><a href="news.html">News</a></li>
								
							</ul>
						</div>
						
						<div class="col-md-4 col-sm-12">
							<h4>Contacts</h4>
							<ul>
								<li><a href="support.html">Support Form</a></li>
								<li><a style="text-transform:none;" href="mailto:Email">Email</a></li>
								<li><a href="#"target="_blank"><img src="assets/img/telegram.png"/> Telegram Chat</a></li>
							
							</ul>	
						</div>
						
						<div class="col-md-4 col-sm-12">
							<h4>Location</h4>
						
							<div class="footer-info">
							
								<p>20 Guild Rd, Charlton, London, England, SE7 8HN United Kingdom</p>
								
							</div>	
							
						
							
						</div>
				
						
						
					</div>
				
				</div>
				
				
				
			
			</div>
			
			
		
		</div>
		

    </div>

		
	</div>
</section>










<script type="text/javascript">
//// Get the CryptoCurrency Information from the API
jQuery.ajax({
	url: "https://min-api.cryptocompare.com/data/pricemultifull",
	data: "fsyms=BTC,ETH,DASH,LTC,BNB,XRP,BCH,XLM,TRX,DOGE&tsyms=USD",
	dataType : 'json',
}).done(function(data) 
{
    // console.log( "BTC : " + data.RAW.BTC.USD.CHANGEPCTDAY + ", ETH : " + data.RAW.ETH.USD.CHANGEPCTDAY + ", DASH : " + data.RAW.DASH.USD.CHANGEPCTDAY + ", LTC : " + data.RAW.LTC.USD.CHANGEPCTDAY + ", XRP : " + data.RAW.XRP.USD.CHANGEPCTDAY );
    //	console.log( "BTC : " + parseFloat(data.RAW.BTC.USD.CHANGEPCTDAY).toFixed(2) + ", ETH : " + parseFloat(data.RAW.ETH.USD.CHANGEPCTDAY).toFixed(2) + ", DASH : " + parseFloat(data.RAW.DASH.USD.CHANGEPCTDAY).toFixed(2) + ", LTC : " + parseFloat(data.RAW.LTC.USD.CHANGEPCTDAY).toFixed(2) + ", XRP : " + parseFloat(data.RAW.XRP.USD.CHANGEPCTDAY).toFixed(2) );

	jQuery(".dashCoin").html('$' + parseFloat(data.RAW.DASH.USD.PRICE).toFixed(2));
	jQuery(".ethCoin").html('$' + parseFloat(data.RAW.ETH.USD.PRICE).toFixed(2));
	jQuery(".bitCoin").html('$' + parseFloat(data.RAW.BTC.USD.PRICE).toFixed(2));
	jQuery(".liteCoin").html('$' + parseFloat(data.RAW.LTC.USD.PRICE).toFixed(2));
	jQuery(".bnbCoin").html('$' + parseFloat(data.RAW.BNB.USD.PRICE).toFixed(2));
	jQuery(".xrpCoin").html('$' + parseFloat(data.RAW.XRP.USD.PRICE).toFixed(2));
	jQuery(".bchCoin").html('$' + parseFloat(data.RAW.BCH.USD.PRICE).toFixed(2));
	jQuery(".xlmCoin").html('$' + parseFloat(data.RAW.XLM.USD.PRICE).toFixed(2));
	jQuery(".dogeCoin").html('$' + parseFloat(data.RAW.DOGE.USD.PRICE).toFixed(2));
	jQuery(".trxCoin").html('$' + parseFloat(data.RAW.TRX.USD.PRICE).toFixed(2));


	var dash = parseFloat(data.RAW.DASH.USD.CHANGEPCTDAY).toFixed(2);
	var eth  = parseFloat(data.RAW.ETH.USD.CHANGEPCTDAY).toFixed(2);
	var btc = parseFloat(data.RAW.BTC.USD.CHANGEPCTDAY).toFixed(2);
	var usd  = parseFloat(data.RAW.LTC.USD.CHANGEPCTDAY).toFixed(2);
	var bnb = parseFloat(data.RAW.BNB.USD.CHANGEPCTDAY).toFixed(2);
	var xrp  = parseFloat(data.RAW.XRP.USD.CHANGEPCTDAY).toFixed(2);
	var bch  = parseFloat(data.RAW.BCH.USD.CHANGEPCTDAY).toFixed(2);
	var xlm  = parseFloat(data.RAW.XLM.USD.CHANGEPCTDAY).toFixed(2);
	var doge  = parseFloat(data.RAW.DOGE.USD.CHANGEPCTDAY).toFixed(2);
	var trx  = parseFloat(data.RAW.TRX.USD.CHANGEPCTDAY).toFixed(2);

	if( dash >= 0 ) jQuery(".dashCoin_change").addClass("greenup"); else jQuery(".dashCoin_change").addClass("purpledown");
	if( eth >= 0 ) jQuery(".ethCoin_change").addClass("greenup"); else jQuery(".ethCoin_change").addClass("purpledown");
	if( btc >= 0 ) jQuery(".bitCoin_change").addClass("greenup"); else jQuery(".bitCoin_change").addClass("purpledown");
	if( usd >= 0 ) jQuery(".liteCoin_change").addClass("greenup"); else jQuery(".liteCoin_change").addClass("purpledown");
	if( bnb >= 0 ) jQuery(".bnbCoin_change").addClass("greenup"); else jQuery(".bnbCoin_change").addClass("purpledown");
	if( xrp >= 0 ) jQuery(".xrpCoin_change").addClass("greenup"); else jQuery(".xrpCoin_change").addClass("purpledown");
	if( bch >= 0 ) jQuery(".bchCoin_change").addClass("greenup"); else jQuery(".bchCoin_change").addClass("purpledown");
	if( xlm >= 0 ) jQuery(".xlmCoin_change").addClass("greenup"); else jQuery(".xlmCoin_change").addClass("purpledown");
	if( doge >= 0 ) jQuery(".dogeCoin_change").addClass("greenup"); else jQuery(".dogeCoin_change").addClass("purpledown");
	if( trx >= 0 ) jQuery(".trxCoin_change").addClass("greenup"); else jQuery(".trxCoin_change").addClass("purpledown");
	
	
	
	if( dash >= 0 ) jQuery(".dashCoin_change1").addClass("greenup1"); else jQuery(".dashCoin_change1").addClass("purpledown1");
	if( eth >= 0 ) jQuery(".ethCoin_change1").addClass("greenup1"); else jQuery(".ethCoin_change1").addClass("purpledown1");
	if( btc >= 0 ) jQuery(".bitCoin_change1").addClass("greenup1"); else jQuery(".bitCoin_change1").addClass("purpledown1");
	if( usd >= 0 ) jQuery(".liteCoin_change1").addClass("greenup1"); else jQuery(".liteCoin_change1").addClass("purpledown1");
	if( bnb >= 0 ) jQuery(".bnbCoin_change1").addClass("greenup1"); else jQuery(".bnbCoin_change1").addClass("purpledown1");
	if( xrp >= 0 ) jQuery(".xrpCoin_change1").addClass("greenup1"); else jQuery(".xrpCoin_change1").addClass("purpledown1");
	if( bch >= 0 ) jQuery(".bchCoin_change1").addClass("greenup1"); else jQuery(".bchCoin_change1").addClass("purpledown1");
	if( xlm >= 0 ) jQuery(".xlmCoin_change1").addClass("greenup1"); else jQuery(".xlmCoin_change1").addClass("purpledown1");
	if( doge >= 0 ) jQuery(".dogeCoin_change1").addClass("greenup1"); else jQuery(".dogeCoin_change1").addClass("purpledown1");
	if( trx >= 0 ) jQuery(".trxCoin_change1").addClass("greenup1"); else jQuery(".trxCoin_change1").addClass("purpledown1");
	

	jQuery(".dashCoin_change").html( dash + "%");
	jQuery(".ethCoin_change").html( eth + "%");
	jQuery(".bitCoin_change").html( btc + "%");
	jQuery(".liteCoin_change").html( usd + "%");
	jQuery(".bnbCoin_change").html( bnb + "%");
	jQuery(".xrpCoin_change").html( xrp + "%");
	jQuery(".bchCoin_change").html( bch + "%");
	jQuery(".xlmCoin_change").html( bch + "%");
	jQuery(".dogeCoin_change").html( doge + "%");
	jQuery(".trxCoin_change").html( trx + "%");

    // VOLUME INFORMATION
    jQuery(".dashCoin_volume").html('Volume $' + data.RAW.DASH.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".ethCoin_volume").html('Volume $' + data.RAW.ETH.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".bitCoin_volume").html('Volume $' + data.RAW.BTC.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".liteCoin_volume").html('Volume $' + data.RAW.LTC.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".bnbCoin_volume").html('Volume $' + data.RAW.BNB.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".xrpCoin_volume").html('Volume $' + data.RAW.XRP.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".bchCoin_volume").html('Volume $' + data.RAW.BCH.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".xlmCoin_volume").html('Volume $' + data.RAW.XLM.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".dogeCoin_volume").html('Volume $' + data.RAW.DOGE.USD.VOLUME24HOUR.toFixed(2));
	jQuery(".trxCoin_volume").html('Volume $' + data.RAW.TRX.USD.VOLUME24HOUR.toFixed(2));

});


</script>



          
</body>
</html>




