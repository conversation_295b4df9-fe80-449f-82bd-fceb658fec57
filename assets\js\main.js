

$(document).ready(function(){
	wow = new WOW(
      {
        animateClass: 'animated',
        offset:       100,
        callback:     function(box) {
          console.log("WOW: animating <" + box.tagName.toLowerCase() + ">")
        }
      }
    );
    wow.init();
    
      
	var percent = document.getElementById("Ultra").value;	

	var percent =  parseFloat(document.getElementById("Ultra").value); 
	var minMoney 	= [25,1000.00,3000.00,50.00,100.00 ];
	var maxMoney	= [5001.00,5001.00,8001.00,5001.00,5001.00 ];
	$("#money").val(minMoney[0]);
	$("#money-slider").val(minMoney[0]);

	//Calculator
	function calc(){
		var money = parseFloat($("#money").val());
		switch (percent) {
		   
			case 0:
		        if ( money >= 25 && money <= 2000){

		        	var profitDaily = money / 100 * 2.55;
					var profitDaily = profitDaily.toFixed(2);
					var profitTotal = money / 100 * 2.55 * 65 + money;
					var profitTotal = profitTotal.toFixed(2);
					var profitPercent = 101;
					var profitPercent = profitPercent.toFixed(2);
					var profitNet = money / 100 * 2 * 90;
					var profitNet = profitNet.toFixed(2);

					$("#profitDaily").text(profitDaily);
					$("#profitTotal").text(profitTotal);
					$("#profitPercent").text(profitPercent + '%');
					$("#profitNet").text('$' + profitNet);

		        } if (money >= 2001 && money <= 3000) {
		        	var profitDaily = money / 100 * 3.55;
					var profitDaily = profitDaily.toFixed(2);
					var profitTotal = money / 100 * 3.55 * 65 + money;
					var profitTotal = profitTotal.toFixed(2);
					var profitPercent = 105;
					var profitPercent = profitPercent.toFixed(2);
					var profitNet = money / 100 * 2.5 * 90;
					var profitNet = profitNet.toFixed(2);

					$("#profitDaily").text(profitDaily);
					$("#profitTotal").text(profitTotal);
					$("#profitPercent").text(profitPercent + '%');
					$("#profitNet").text('$' + profitNet);
					
		        } if (money >= 3001 && money <= 5000) {
		        	var profitDaily = money / 100 * 4.55;
					var profitDaily = profitDaily.toFixed(2);
					var profitTotal = money / 100 * 4.55 * 65 + money;
					var profitTotal = profitTotal.toFixed(2);
					var profitPercent = 110;
					var profitPercent = profitPercent.toFixed(2);
					var profitNet = money / 100 * 3 * 90;
					var profitNet = profitNet.toFixed(2);
					

					$("#profitDaily").text(profitDaily);
					$("#profitTotal").text(profitTotal);
					$("#profitPercent").text(profitPercent + '%');
					$("#profitNet").text('$' + profitNet);
					
				} if (money >= 5000 && money < 100000) {
		        	var profitDaily = money / 100 * 5.55;
					var profitDaily = profitDaily.toFixed(2);
					var profitTotal = money / 100 * 5.55 * 65 + money;
					var profitTotal = profitTotal.toFixed(2);
					var profitPercent = 120;
					var profitPercent = profitPercent.toFixed(2);
					var profitNet = money / 100 * 3 * 90;
					var profitNet = profitNet.toFixed(2);
					

					$("#profitDaily").text(profitDaily);
					$("#profitTotal").text(profitTotal);
					$("#profitPercent").text(profitPercent + '%');
					$("#profitNet").text('$' + profitNet);	
			
					
					
					
				//} else if(isNaN(money) == true) {
				} if (money < 20) {
					$("#profitDaily").text("Min: $20");
					$("#profitTotal").text("Min: $20");
					$("#profitPercent").text("Min: $20");
					$("#profitNet").text("Min: $20");
				}
		        break;
		   	case 1:
		        if ( money >= 3000 && money <= 100000){

		        	var profitDaily = money / 100 * 555 / 25;
					var profitDaily = profitDaily.toFixed(2);
					var profitTotal = money / 100 * 555;
					var profitTotal = profitTotal.toFixed(2);
					var profitPercent = 106;
					var profitPercent = profitPercent.toFixed(2);
					var profitNet = money / 100 * 2 * 90;
					var profitNet = profitNet.toFixed(2);

					$("#profitDaily").text(profitDaily);
					$("#profitTotal").text(profitTotal);
					$("#profitPercent").text(profitPercent + '%');
					$("#profitNet").text('$' + profitNet);

		     
					
					
				//} else if(isNaN(money) == true) {
				} if (money < 501) {
					$("#profitDaily").text("Min: $3000");
					$("#profitTotal").text("Min: $3000");
					$("#profitPercent").text("Min: $3000");
					$("#profitNet").text("Min: $3000");
				}
		        break;
		   	case 2:
		        if ( money >= 1000 && money <= 100000){

		        	var profitDaily = money / 100 * 655 / 35;
					var profitDaily = profitDaily.toFixed(2);
					var profitTotal = money / 100 * 655;
					var profitTotal = profitTotal.toFixed(2);
					var profitPercent = 106;
					var profitPercent = profitPercent.toFixed(2);
					var profitNet = money / 100 * 2 * 90;
					var profitNet = profitNet.toFixed(2);

					$("#profitDaily").text(profitDaily);
					$("#profitTotal").text(profitTotal);
					$("#profitPercent").text(profitPercent + '%');
					$("#profitNet").text('$' + profitNet);

		     
					
					
				//} else if(isNaN(money) == true) {
				} if (money < 501) {
					$("#profitDaily").text("Min: $3000");
					$("#profitTotal").text("Min: $3000");
					$("#profitPercent").text("Min: $3000");
					$("#profitNet").text("Min: $3000");
				}
		        break;
		        
		   	case 3:
		        if ( money >= 20 && money <= 100000){

		        	var profitDaily = money / 100 * 1555 / 75;
					var profitDaily = profitDaily.toFixed(2);
					var profitTotal = money / 100 * 1555;
					var profitTotal = profitTotal.toFixed(2);
					var profitPercent = 106;
					var profitPercent = profitPercent.toFixed(2);
					var profitNet = money / 100 * 2 * 90;
					var profitNet = profitNet.toFixed(2);

					$("#profitDaily").text(profitDaily);
					$("#profitTotal").text(profitTotal);
					$("#profitPercent").text(profitPercent + '%');
					$("#profitNet").text('$' + profitNet);

		     
					
					
				//} else if(isNaN(money) == true) {
				} if (money < 20) {
					$("#profitDaily").text("Min: $20");
					$("#profitTotal").text("Min: $20");
					$("#profitPercent").text("Min: $20");
					$("#profitNet").text("Min: $20");
				}
		        break;
		   
		     

		}
	}
	if($("#money").length){
		calc();
	}
	$("#money").keyup(function(){
		calc();
	});

	$("#Ultra").on('change', function() {
		percent = parseFloat(this.value);
		calc();
	})

  
});
